"""
闲鱼Cookie监控器模块

提供闲鱼网站的Cookie监控功能，自动检测和刷新_m_h5_tk_enc等关键Cookie
支持后台持续监控和页面对象暴露

Author: AI Assistant
Date: 2025-07-29
"""

import datetime
import asyncio
from typing import Optional, Dict, Any

from browser_manager import SingletonBrowserManager


class GoofishCookieMonitor:
    """
    闲鱼Cookie监控管理器
    
    负责访问闲鱼网站、监控关键Cookie的有效期，并在需要时自动刷新。
    提供页面对象供外部使用，支持后台持续监控。
    """
    
    def __init__(self, browser_manager=None):
        """
        初始化监控器

        Args:
            browser_manager: 可选的浏览器管理器实例，如果不提供则创建新的单例实例
        """
        self.page = None
        self.monitor_task = None
        self.is_monitoring = False
        # 如果传入了browser_manager则使用，否则创建单例实例
        self.browser_manager = browser_manager if browser_manager else SingletonBrowserManager()
        self.target_cookies = ['_m_h5_tk_enc', '_m_h5_tk']  # 监控的目标Cookie
        self.check_interval = 300  # 默认5分钟检查间隔
        self.expire_threshold = 1800  # 30分钟过期阈值
    
    async def initialize(self, headless: bool = False, auto_start_monitor: bool = True):
        """
        初始化监控器
        
        Args:
            headless: 是否以无头模式运行
            auto_start_monitor: 是否自动启动监控
            
        Returns:
            page: 页面对象，供外部使用
            
        Raises:
            Exception: 初始化失败时抛出异常
        """
        try:
            print("🔄 正在初始化闲鱼Cookie监控器...")
            
            # 获取单例浏览器
            browser = await self.browser_manager.get_browser(headless)
            
            # 创建带反检测功能的页面
            self.page = await self.browser_manager.create_stealth_page()
            print("✅ 创建闲鱼监控页面成功")
            
            # 访问闲鱼网站
            print("🔗 正在访问闲鱼网站...")
            await self.page.goto('https://www.goofish.com/', {
                'waitUntil': 'networkidle0',
                'timeout': 30000
            })
            print("✅ 闲鱼网站加载完成")
            
            # 初始Cookie检查
            print("🔍 进行初始Cookie检查...")
            await self._ensure_valid_cookies()
            
            # 自动启动监控
            if auto_start_monitor:
                await self.start_monitoring()
            
            print("✅ 闲鱼Cookie监控器初始化完成")
            return self.page
            
        except Exception as e:
            print(f"❌ 初始化闲鱼监控器失败: {str(e)}")
            raise
    
    async def start_monitoring(self, check_interval: int = 300):
        """
        启动Cookie监控
        
        Args:
            check_interval: 检查间隔（秒），默认5分钟
        """
        if self.is_monitoring:
            print("⚠️  Cookie监控已在运行中")
            return
        
        if not self.page:
            raise Exception("监控器未初始化，请先调用initialize()")
        
        self.check_interval = check_interval
        print(f"🔄 启动Cookie监控，每{check_interval//60}分钟检查一次...")
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
    
    async def stop_monitoring(self):
        """停止Cookie监控"""
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            self.monitor_task = None
        
        self.is_monitoring = False
        print("🛑 Cookie监控已停止")
    
    async def get_page(self):
        """
        获取页面对象
        
        Returns:
            Page对象，供外部使用
            
        Raises:
            Exception: 监控器未初始化时抛出异常
        """
        if not self.page:
            raise Exception("监控器未初始化，请先调用initialize()")
        return self.page
    
    async def get_cookies(self, domain: str = 'https://www.goofish.com') -> list:
        """
        获取指定域名的Cookie
        
        Args:
            domain: 域名，默认为闲鱼域名
            
        Returns:
            Cookie列表
        """
        if not self.page:
            raise Exception("监控器未初始化，请先调用initialize()")
        
        return await self.page.cookies(domain)
    
    async def get_target_cookies(self) -> Dict[str, Any]:
        """
        获取目标Cookie信息
        
        Returns:
            目标Cookie的字典，包含名称、值、过期时间等信息
        """
        cookies = await self.get_cookies()
        target_info = {}
        
        for cookie in cookies:
            if cookie['name'] in self.target_cookies:
                target_info[cookie['name']] = {
                    'value': cookie['value'],
                    'expires': cookie.get('expires'),
                    'domain': cookie.get('domain'),
                    'path': cookie.get('path')
                }
        
        return target_info
    
    async def check_cookie_validity(self, cookie_name: str = '_m_h5_tk_enc') -> bool:
        """
        检查指定Cookie的有效期
        
        Args:
            cookie_name: Cookie名称，默认为_m_h5_tk_enc
            
        Returns:
            bool: True表示Cookie有效，False表示已过期或不存在
        """
        try:
            cookies = await self.get_cookies()
            
            # 查找目标Cookie
            target_cookie = None
            for cookie in cookies:
                if cookie['name'] == cookie_name:
                    target_cookie = cookie
                    break
            
            if not target_cookie:
                print(f"⚠️  未找到Cookie: {cookie_name}")
                return False
            
            expires = target_cookie.get('expires', None)
            if expires is None:
                print(f"🍪 {cookie_name}: 会话Cookie（浏览器关闭时过期）")
                return True  # 会话Cookie认为有效
            
            # 计算剩余时间
            expire_time = datetime.datetime.fromtimestamp(expires)
            current_time = datetime.datetime.now()
            time_remaining = expire_time - current_time
            
            print(f"🍪 {cookie_name} 剩余时间: {time_remaining}")
            
            # 检查是否在阈值内
            return time_remaining.total_seconds() > self.expire_threshold
            
        except Exception as e:
            print(f"❌ 检查Cookie有效期出错: {str(e)}")
            return False
    
    async def refresh_page(self):
        """刷新页面以更新Cookie"""
        try:
            print("🔄 正在刷新闲鱼页面以更新Cookie...")
            
            await self.page.goto('https://www.goofish.com/', {
                'waitUntil': 'networkidle2', 
                'timeout': 30000
            })
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 页面交互以确保Cookie更新
            await self.page.evaluate('window.scrollTo(0, 500)')
            await asyncio.sleep(1)
            await self.page.evaluate('window.scrollTo(0, 0)')
            
            print("✅ 页面刷新完成")
            
        except Exception as e:
            print(f"❌ 刷新页面出错: {str(e)}")
            raise
    
    async def _monitor_loop(self):
        """监控循环（内部方法）"""
        while self.is_monitoring:
            try:
                await asyncio.sleep(self.check_interval)
                
                if not self.is_monitoring:
                    break
                
                current_time = datetime.datetime.now().strftime('%H:%M:%S')
                print(f"\n⏰ [{current_time}] 定期检查Cookie有效期...")
                
                is_valid = await self._ensure_valid_cookies()
                
                if not is_valid:
                    print("❌ Cookie监控检测到无效Cookie！")
                else:
                    print("✅ Cookie监控：状态正常")
                    
            except asyncio.CancelledError:
                print("🛑 Cookie监控任务被取消")
                break
            except Exception as e:
                print(f"❌ Cookie监控出错: {str(e)}")
                # 出错后等待1分钟再继续
                await asyncio.sleep(60)
    
    async def _ensure_valid_cookies(self) -> bool:
        """确保所有目标Cookie有效（内部方法）"""
        try:
            all_valid = True
            
            # 检查所有目标Cookie
            for cookie_name in self.target_cookies:
                is_valid = await self.check_cookie_validity(cookie_name)
                if not is_valid:
                    all_valid = False
                    break
            
            # 如果有Cookie无效，刷新页面
            if not all_valid:
                print("🔄 检测到Cookie无效，正在刷新页面...")
                await self.refresh_page()
                
                # 刷新后再次检查
                print("🔍 刷新后重新检查Cookie...")
                all_valid = True
                for cookie_name in self.target_cookies:
                    is_valid = await self.check_cookie_validity(cookie_name)
                    if not is_valid:
                        all_valid = False
                        break
                
                if all_valid:
                    print("✅ Cookie刷新成功")
                else:
                    print("❌ Cookie刷新失败，可能需要手动处理")
            
            return all_valid
            
        except Exception as e:
            print(f"❌ 确保Cookie有效时出错: {str(e)}")
            return False
    
    async def close(self):
        """关闭监控器"""
        print("🔄 正在关闭闲鱼Cookie监控器...")
        
        # 停止监控
        await self.stop_monitoring()
        
        # 关闭页面
        if self.page:
            try:
                await self.page.close()
                print("✅ 监控页面已关闭")
            except Exception as e:
                print(f"⚠️  关闭页面时出错: {str(e)}")
            finally:
                self.page = None
        
        print("✅ 闲鱼Cookie监控器已关闭")
    
    def is_active(self) -> bool:
        """
        检查监控器是否活跃
        
        Returns:
            bool: True表示监控器已初始化且页面存在
        """
        return self.page is not None
    
    def get_monitor_status(self) -> Dict[str, Any]:
        """
        获取监控器状态信息
        
        Returns:
            包含监控器状态的字典
        """
        return {
            'is_active': self.is_active(),
            'is_monitoring': self.is_monitoring,
            'check_interval': self.check_interval,
            'expire_threshold': self.expire_threshold,
            'target_cookies': self.target_cookies
        }


# 便捷函数
async def create_goofish_monitor(headless: bool = False, auto_start_monitor: bool = True, browser_manager=None):
    """
    创建闲鱼Cookie监控器的便捷函数

    Args:
        headless: 是否以无头模式运行
        auto_start_monitor: 是否自动启动监控
        browser_manager: 可选的浏览器管理器实例，确保使用同一个浏览器实例

    Returns:
        GoofishCookieMonitor实例
    """
    # 如果没有提供browser_manager，先创建单例实例
    if browser_manager is None:
        browser_manager = SingletonBrowserManager()

    monitor = GoofishCookieMonitor(browser_manager)
    await monitor.initialize(headless, auto_start_monitor)
    return monitor


# 移除 get_goofish_cookies 便捷函数
# 推荐使用方式：
# monitor = await create_goofish_monitor(headless=True, auto_start_monitor=False)
# cookies = await monitor.get_cookies(domain)
# await monitor.close()
