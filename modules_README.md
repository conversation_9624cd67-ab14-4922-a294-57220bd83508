# 单例浏览器管理器 + 闲鱼Cookie监控器

## 📋 项目概述

本项目提供了两个核心模块，用于解决Pyppeteer开发中的常见需求：

1. **`browser_manager.py`** - 单例浏览器管理器
2. **`goofish_monitor.py`** - 闲鱼Cookie监控器

## 🎯 核心特性

### 单例浏览器管理器
- ✅ **全局唯一浏览器实例** - 避免重复创建浏览器的开销
- ✅ **跨平台Chrome路径查找** - 自动检测Windows/macOS/Linux系统的Chrome
- ✅ **反检测配置** - 内置完整的反检测启动参数
- ✅ **资源管理** - 统一的浏览器生命周期管理
- ✅ **便捷函数** - 提供简单易用的API接口

### 闲鱼Cookie监控器
- ✅ **自动访问闲鱼网站** - 初始化时自动加载并应用反检测
- ✅ **Cookie有效期监控** - 实时监控`_m_h5_tk_enc`等关键Cookie
- ✅ **后台持续监控** - 独立异步任务，不阻塞主程序
- ✅ **自动刷新机制** - Cookie过期时自动刷新页面
- ✅ **页面对象暴露** - 提供page对象供外部使用

## 📦 安装依赖

```bash
pip install pyppeteer pyppeteer-stealth
```

## 🚀 快速开始

### 基础使用

```python
import asyncio
from goofish_monitor import create_goofish_monitor

async def main():
    # 创建闲鱼监控器（自动使用单例浏览器）
    monitor = await create_goofish_monitor(headless=False)
    
    # 获取页面对象供后续使用
    page = await monitor.get_page()
    
    # 使用页面进行操作
    title = await page.title()
    print(f"页面标题: {title}")
    
    # 获取Cookie
    cookies = await monitor.get_cookies()
    print(f"Cookie数量: {len(cookies)}")
    
    # 清理资源
    await monitor.close()

asyncio.run(main())
```

### 高级使用

```python
import asyncio
from browser_manager import get_singleton_browser, create_stealth_page
from goofish_monitor import create_goofish_monitor

async def advanced_usage():
    # 获取单例浏览器
    browser = await get_singleton_browser(headless=False)
    
    # 创建多个页面（共享同一浏览器）
    page1 = await create_stealth_page()
    page2 = await create_stealth_page()
    
    # 创建闲鱼监控器
    monitor = await create_goofish_monitor()
    monitor_page = await monitor.get_page()
    
    # 所有页面都使用同一个浏览器实例
    print(f"页面1浏览器ID: {id(page1.browser)}")
    print(f"页面2浏览器ID: {id(page2.browser)}")
    print(f"监控页面浏览器ID: {id(monitor_page.browser)}")
    
    # 清理资源
    await page1.close()
    await page2.close()
    await monitor.close()

asyncio.run(advanced_usage())
```

## 📚 API 文档

### SingletonBrowserManager

#### 主要方法

- `get_browser(headless=False)` - 获取单例浏览器实例
- `create_stealth_page()` - 创建带反检测功能的页面
- `close_browser()` - 关闭浏览器实例
- `is_browser_active()` - 检查浏览器是否活跃

#### 便捷函数

- `get_singleton_browser(headless=False)` - 获取单例浏览器
- `create_stealth_page(headless=False)` - 创建反检测页面
- `close_singleton_browser()` - 关闭单例浏览器

### GoofishCookieMonitor

#### 主要方法

- `initialize(headless=False, auto_start_monitor=True)` - 初始化监控器
- `start_monitoring(check_interval=300)` - 启动Cookie监控
- `stop_monitoring()` - 停止Cookie监控
- `get_page()` - 获取页面对象
- `get_cookies(domain)` - 获取指定域名的Cookie
- `check_cookie_validity(cookie_name)` - 检查Cookie有效期
- `refresh_page()` - 刷新页面更新Cookie
- `close()` - 关闭监控器

#### 便捷函数

- `create_goofish_monitor(headless=False, auto_start_monitor=True)` - 创建监控器
- `get_goofish_cookies(domain)` - 快速获取闲鱼Cookie

## 🔧 配置选项

### 浏览器配置

```python
# 无头模式
browser = await get_singleton_browser(headless=True)

# 有头模式（默认）
browser = await get_singleton_browser(headless=False)
```

### 监控配置

```python
# 自定义检查间隔（秒）
await monitor.start_monitoring(check_interval=600)  # 10分钟

# 自定义过期阈值
monitor.expire_threshold = 3600  # 1小时

# 自定义监控的Cookie
monitor.target_cookies = ['_m_h5_tk_enc', '_m_h5_tk', 'custom_cookie']
```

## 🌍 使用场景

### 1. 长时间运行的爬虫

```python
async def long_running_crawler():
    monitor = await create_goofish_monitor(headless=True)
    page = await monitor.get_page()
    
    for i in range(1000):  # 长时间运行
        # 检查Cookie有效性
        if not await monitor.check_cookie_validity('_m_h5_tk_enc'):
            print("Cookie无效，停止爬取")
            break
        
        # 执行爬取任务
        await perform_crawling_task(page)
        await asyncio.sleep(10)
    
    await monitor.close()
```

### 2. 多页面并发操作

```python
async def multi_page_operations():
    browser = await get_singleton_browser()
    
    # 创建多个页面
    pages = []
    for i in range(5):
        page = await create_stealth_page()
        pages.append(page)
    
    # 并发执行任务
    tasks = [process_page(page) for page in pages]
    await asyncio.gather(*tasks)
    
    # 清理
    for page in pages:
        await page.close()
```

### 3. Cookie状态监控

```python
async def monitor_cookie_status():
    monitor = await create_goofish_monitor()
    
    # 获取监控状态
    status = monitor.get_monitor_status()
    print(f"监控状态: {status}")
    
    # 获取目标Cookie信息
    target_cookies = await monitor.get_target_cookies()
    for name, info in target_cookies.items():
        print(f"{name}: {info}")
    
    await monitor.close()
```

## ⚠️ 注意事项

1. **依赖安装**：确保安装了`pyppeteer`和`pyppeteer-stealth`
2. **Chrome路径**：程序会自动查找Chrome，如果找不到请手动安装Chrome
3. **资源清理**：使用完毕后记得调用`close()`方法清理资源
4. **异常处理**：建议在实际使用中添加适当的异常处理
5. **网络环境**：确保能够正常访问闲鱼网站

## 🔍 故障排除

### 常见问题

1. **Chrome未找到**
   ```
   解决方案：安装Chrome浏览器或设置正确的Chrome路径
   ```

2. **pyppeteer-stealth未安装**
   ```bash
   pip install pyppeteer-stealth
   ```

3. **页面加载超时**
   ```python
   # 增加超时时间
   await page.goto(url, {'timeout': 60000})
   ```

4. **Cookie监控失效**
   ```python
   # 手动刷新页面
   await monitor.refresh_page()
   ```

## 📝 更新日志

### v1.0.0 (2025-07-29)
- ✅ 初始版本发布
- ✅ 实现单例浏览器管理器
- ✅ 实现闲鱼Cookie监控器
- ✅ 提供完整的使用示例和文档

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
