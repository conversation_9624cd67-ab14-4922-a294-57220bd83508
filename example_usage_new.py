"""
重构后的使用示例：AppManager + 单例模式

演示如何使用新的应用架构：
1. 使用AppManager进行统一初始化
2. 初始化后直接使用各个单例类
3. 保持单例模式的纯粹性

Author: AI Assistant
Date: 2025-07-29
"""

import asyncio
from browser_manager import get_singleton_browser, create_stealth_page, SingletonBrowserManager
from goofish_monitor import create_goofish_monitor, GoofishCookieMonitor
from app_manager import initialize_app, cleanup_app, is_app_initialized, get_app_status


async def example_with_app_manager():
    """使用AppManager的推荐方式"""
    print("🎯 示例1：使用AppManager统一初始化")
    print("=" * 50)
    
    try:
        # 1. 应用启动时统一初始化
        print("\n📋 步骤1: 统一初始化应用")
        await initialize_app({
            'headless': False,
            'init_goofish_monitor': True,  # 预初始化闲鱼监控器
            'auto_start_monitor': True
        })
        
        # 2. 检查初始化状态
        print(f"\n📋 步骤2: 检查初始化状态")
        print(f"   应用已初始化: {is_app_initialized()}")
        status = get_app_status()
        print(f"   浏览器活跃: {status.get('browser_active', False)}")
        print(f"   监控器活跃: {status.get('monitor_active', False)}")
        
        # 3. 初始化完成后，直接使用各个单例类
        print(f"\n📋 步骤3: 直接使用单例类")
        
        # 使用浏览器管理器
        browser_manager = SingletonBrowserManager()
        page = await browser_manager.create_stealth_page()
        print(f"   ✅ 创建页面成功: {type(page)}")
        
        # 使用闲鱼监控器
        monitor = GoofishCookieMonitor()  # 由于预初始化，这里直接获取已初始化的实例
        monitor_page = await monitor.get_page()
        title = await monitor_page.title()
        print(f"   ✅ 获取页面标题: {title}")
        
        # 4. 获取Cookie信息
        print(f"\n📋 步骤4: 获取Cookie信息")
        cookies = await monitor.get_cookies()
        print(f"   🍪 Cookie数量: {len(cookies)}")
        
        target_cookies = await monitor.get_target_cookies()
        for name, info in target_cookies.items():
            print(f"   🎯 {name}: {info['value'][:20]}...")
        
        # 5. 模拟业务操作
        print(f"\n📋 步骤5: 模拟业务操作")
        await page.goto('https://example.com')
        example_title = await page.title()
        print(f"   📄 访问示例网站: {example_title}")
        
        # 关闭额外创建的页面
        await page.close()
        
        print("\n✅ 示例1完成")
        
    except Exception as e:
        print(f"❌ 示例1出错: {str(e)}")
    
    finally:
        # 6. 应用结束时统一清理
        print(f"\n📋 步骤6: 统一清理资源")
        await cleanup_app()


async def example_without_app_manager():
    """不使用AppManager的传统方式"""
    print("\n🎯 示例2：传统方式（不使用AppManager）")
    print("=" * 50)
    
    try:
        # 1. 直接使用便捷函数初始化
        print("\n📋 步骤1: 使用便捷函数")
        browser = await get_singleton_browser(headless=False)
        page = await create_stealth_page()
        monitor = await create_goofish_monitor(headless=False, auto_start_monitor=True)
        
        print(f"   ✅ 浏览器实例: {type(browser)}")
        print(f"   ✅ 页面实例: {type(page)}")
        print(f"   ✅ 监控器实例: {type(monitor)}")
        
        # 2. 使用组件
        print(f"\n📋 步骤2: 使用组件")
        await page.goto('https://httpbin.org/get')
        title = await page.title()
        print(f"   📄 页面标题: {title}")
        
        cookies = await monitor.get_cookies()
        print(f"   🍪 Cookie数量: {len(cookies)}")
        
        # 3. 手动清理
        print(f"\n📋 步骤3: 手动清理资源")
        await page.close()
        await monitor.close()
        
        # 使用单例管理器清理浏览器
        browser_manager = SingletonBrowserManager()
        await browser_manager.close_browser()
        
        print("\n✅ 示例2完成")
        
    except Exception as e:
        print(f"❌ 示例2出错: {str(e)}")


async def example_mixed_usage():
    """混合使用方式"""
    print("\n🎯 示例3：混合使用方式")
    print("=" * 50)
    
    try:
        # 1. 使用AppManager初始化基础组件
        print("\n📋 步骤1: AppManager初始化基础组件")
        await initialize_app({
            'headless': True,  # 无头模式
            'init_goofish_monitor': False  # 不预初始化监控器
        })
        
        # 2. 根据需要创建额外的组件
        print(f"\n📋 步骤2: 按需创建组件")
        
        # 创建多个页面（共享同一浏览器）
        browser_manager = SingletonBrowserManager()
        page1 = await browser_manager.create_stealth_page()
        page2 = await browser_manager.create_stealth_page()
        
        print(f"   ✅ 页面1浏览器ID: {id(page1.browser)}")
        print(f"   ✅ 页面2浏览器ID: {id(page2.browser)}")
        print(f"   🔗 使用同一浏览器: {id(page1.browser) == id(page2.browser)}")
        
        # 按需创建监控器
        monitor = await create_goofish_monitor(auto_start_monitor=False)
        print(f"   ✅ 监控器创建成功，自动监控: {monitor.is_monitoring}")
        
        # 3. 并行操作
        print(f"\n📋 步骤3: 并行操作")
        
        async def task1():
            await page1.goto('https://httpbin.org/delay/1')
            return await page1.title()
        
        async def task2():
            await page2.goto('https://httpbin.org/delay/1')
            return await page2.title()
        
        async def task3():
            return await monitor.get_cookies()
        
        # 并行执行任务
        results = await asyncio.gather(task1(), task2(), task3())
        print(f"   📄 任务1结果: {results[0]}")
        print(f"   📄 任务2结果: {results[1]}")
        print(f"   🍪 任务3结果: {len(results[2])} cookies")
        
        # 4. 清理
        print(f"\n📋 步骤4: 清理资源")
        await page1.close()
        await page2.close()
        await monitor.close()
        
        print("\n✅ 示例3完成")
        
    except Exception as e:
        print(f"❌ 示例3出错: {str(e)}")
    
    finally:
        await cleanup_app()


async def main():
    """主函数：运行所有示例"""
    print("🚀 重构后的应用架构使用示例")
    print("=" * 60)
    
    # 示例1：推荐的AppManager方式
    await example_with_app_manager()
    
    # 等待一下，避免资源冲突
    await asyncio.sleep(2)
    
    # 示例2：传统方式
    await example_without_app_manager()
    
    # 等待一下，避免资源冲突
    await asyncio.sleep(2)
    
    # 示例3：混合使用方式
    await example_mixed_usage()
    
    print("\n🎉 所有示例运行完成！")
    print("\n💡 推荐使用方式：")
    print("   1. 应用启动时使用 initialize_app() 统一初始化")
    print("   2. 业务代码中直接使用各个单例类")
    print("   3. 应用结束时使用 cleanup_app() 统一清理")


if __name__ == "__main__":
    asyncio.run(main())
