"""
单例浏览器管理器模块

提供全局唯一的Pyppeteer浏览器实例管理功能
支持反检测配置和资源管理

Author: AI Assistant
Date: 2025-07-29
"""

import os
import platform
import subprocess
import asyncio
from typing import Optional
from pathlib import Path

# 导入pyppeteer相关模块
try:
    from pyppeteer import launch
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False
    print("❌ Pyppeteer未安装，请运行: pip install pyppeteer")

# 尝试导入pyppeteer_stealth
try:
    from pyppeteer_stealth import stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    print("❌ pyppeteer_stealth未安装，请运行: pip install pyppeteer-stealth")


class SingletonBrowserManager:
    """
    单例浏览器管理器
    
    确保整个应用程序中只有一个浏览器实例，提供统一的配置和资源管理。
    支持反检测配置，自动查找Chrome路径。
    """
    
    _instance = None
    _browser = None
    _chrome_path = None
    _is_initialized = False
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def get_browser(self, headless: bool = False, force_reinit: bool = False):
        """
        获取单例浏览器实例
        
        Args:
            headless: 是否以无头模式运行
            force_reinit: 是否强制重新初始化浏览器
            
        Returns:
            Browser对象
            
        Raises:
            Exception: 当依赖未安装或Chrome未找到时
        """
        if self._browser is None or force_reinit:
            await self._initialize_browser(headless)
        return self._browser
    
    async def _initialize_browser(self, headless: bool = False):
        """
        初始化浏览器实例
        
        Args:
            headless: 是否以无头模式运行
        """
        # 检查依赖
        if not PYPPETEER_AVAILABLE:
            raise Exception("Pyppeteer未安装，请运行: pip install pyppeteer")
        
        if not STEALTH_AVAILABLE:
            raise Exception("pyppeteer-stealth未安装，请运行: pip install pyppeteer-stealth")
        
        # 查找Chrome路径
        if self._chrome_path is None:
            self._chrome_path = self._find_chrome_path()
            if not self._chrome_path:
                raise Exception("未找到Chrome浏览器，请确保已安装Chrome")
        
        print(f"🚀 初始化单例浏览器...")
        print(f"   Chrome路径: {self._chrome_path}")
        print(f"   无头模式: {'是' if headless else '否'}")
        print(f"   反检测模式: 是")
        
        # 反检测启动参数
        args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--disable-blink-features=AutomationControlled',  # 关键反检测参数
            '--disable-infobars',                             # 禁用信息栏
            '--disable-extensions',                           # 禁用扩展
            '--no-default-browser-check',                     # 不检查默认浏览器
            '--disable-default-apps',                         # 禁用默认应用
        ]
        
        try:
            # 启动浏览器
            self._browser = await launch({
                'executablePath': self._chrome_path,
                'headless': headless,
                'args': args,
                'ignoreHTTPSErrors': True,
                'autoClose': False,
                'ignoreDefaultArgs': [
                    '--enable-automation',                    # 忽略自动化标识参数
                    '--enable-blink-features=AutomationControlled'  # 忽略自动化控制特性
                ]
            })
            
            self._is_initialized = True
            print("✅ 单例浏览器初始化成功！")
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {str(e)}")
            self._browser = None
            raise
    
    async def create_stealth_page(self):
        """
        创建带反检测功能的页面
        
        Returns:
            Page对象，已应用反检测设置
        """
        if not self._browser:
            raise Exception("浏览器未初始化，请先调用get_browser()")
        
        # 创建新页面
        page = await self._browser.newPage()
        
        # 应用反检测
        if STEALTH_AVAILABLE:
            await stealth(page)
            print("✅ 页面反检测设置已应用")
        
        # 设置默认视口
        await page.setViewport({'width': 1280, 'height': 720})
        
        return page
    
    async def close_browser(self):
        """关闭浏览器实例"""
        if self._browser:
            try:
                await self._browser.close()
                print("🔒 单例浏览器已关闭")
            except Exception as e:
                print(f"⚠️  关闭浏览器时出错: {str(e)}")
            finally:
                self._browser = None
                self._is_initialized = False
    
    def is_browser_active(self) -> bool:
        """
        检查浏览器是否活跃
        
        Returns:
            bool: True表示浏览器实例存在且活跃
        """
        return self._browser is not None and self._is_initialized
    
    def get_chrome_path(self) -> Optional[str]:
        """
        获取Chrome路径
        
        Returns:
            Chrome可执行文件路径，未找到返回None
        """
        if self._chrome_path is None:
            self._chrome_path = self._find_chrome_path()
        return self._chrome_path
    
    def _find_chrome_path(self) -> Optional[str]:
        """
        跨平台查找Chrome浏览器路径
        
        Returns:
            Chrome可执行文件路径，未找到返回None
        """
        system = platform.system().lower()
        
        if system == "windows":
            return self._find_chrome_windows()
        elif system == "darwin":  # macOS
            return self._find_chrome_macos()
        elif system == "linux":
            return self._find_chrome_linux()
        else:
            print(f"不支持的操作系统: {system}")
            return None
    
    def _find_chrome_windows(self) -> Optional[str]:
        """在Windows系统中查找Chrome路径"""
        possible_paths = [
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        ]
        
        for path in possible_paths:
            if os.path.isfile(path):
                return path
        
        # 通过where命令查找
        try:
            result = subprocess.run(['where', 'chrome'], 
                                  capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                chrome_path = result.stdout.strip().split('\n')[0]
                if os.path.isfile(chrome_path):
                    return chrome_path
        except (subprocess.SubprocessError, FileNotFoundError):
            pass
        
        return None
    
    def _find_chrome_macos(self) -> Optional[str]:
        """在macOS系统中查找Chrome路径"""
        possible_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium",
            os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
        ]
        
        for path in possible_paths:
            if os.path.isfile(path):
                return path
        
        # 使用which命令查找
        try:
            result = subprocess.run(['which', 'google-chrome'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                chrome_path = result.stdout.strip()
                if os.path.isfile(chrome_path):
                    return chrome_path
        except (subprocess.SubprocessError, FileNotFoundError):
            pass
        
        return None
    
    def _find_chrome_linux(self) -> Optional[str]:
        """在Linux系统中查找Chrome路径"""
        possible_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
            "/opt/google/chrome/chrome",
        ]
        
        for path in possible_paths:
            if os.path.isfile(path):
                return path
        
        # 使用which命令查找
        chrome_commands = ["google-chrome", "google-chrome-stable", "chromium-browser", "chromium"]
        
        for cmd in chrome_commands:
            try:
                result = subprocess.run(['which', cmd], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    chrome_path = result.stdout.strip()
                    if os.path.isfile(chrome_path):
                        return chrome_path
            except (subprocess.SubprocessError, FileNotFoundError):
                continue
        
        return None


# 便捷函数
async def get_singleton_browser(headless: bool = False):
    """
    获取单例浏览器实例的便捷函数
    
    Args:
        headless: 是否以无头模式运行
        
    Returns:
        Browser对象
    """
    manager = SingletonBrowserManager()
    return await manager.get_browser(headless)


async def create_stealth_page(headless: bool = False):
    """
    创建带反检测功能的页面的便捷函数
    
    Args:
        headless: 是否以无头模式运行
        
    Returns:
        Page对象，已应用反检测设置
    """
    manager = SingletonBrowserManager()
    await manager.get_browser(headless)
    return await manager.create_stealth_page()


async def close_singleton_browser():
    """关闭单例浏览器的便捷函数"""
    manager = SingletonBrowserManager()
    await manager.close_browser()
