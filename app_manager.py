"""
应用管理器模块

提供统一的应用初始化和资源管理功能
确保正确的初始化顺序和资源清理

Author: AI Assistant
Date: 2025-07-29
"""

import asyncio
from typing import Optional, List
from browser_manager import SingletonBrowserManager
from goofish_monitor import GoofishCookieMonitor


class AppManager:
    """
    应用管理器
    
    负责统一管理浏览器实例和各种监控器，确保正确的初始化顺序和资源清理。
    这是推荐的使用方式，避免了直接操作单例可能带来的问题。
    """
    
    def __init__(self):
        """初始化应用管理器"""
        self.browser_manager = None
        self.monitors = []  # 存储所有创建的监控器
        self.is_initialized = False
    
    async def initialize(self, headless: bool = False):
        """
        初始化应用管理器
        
        Args:
            headless: 是否以无头模式运行浏览器
        """
        if self.is_initialized:
            print("⚠️  应用管理器已经初始化")
            return
        
        print("🚀 正在初始化应用管理器...")
        
        # 1. 首先初始化浏览器管理器
        self.browser_manager = SingletonBrowserManager()
        await self.browser_manager.get_browser(headless)
        print("✅ 浏览器管理器初始化完成")
        
        self.is_initialized = True
        print("✅ 应用管理器初始化完成")
    
    async def create_goofish_monitor(self, auto_start_monitor: bool = True) -> GoofishCookieMonitor:
        """
        创建闲鱼Cookie监控器
        
        Args:
            auto_start_monitor: 是否自动启动监控
            
        Returns:
            GoofishCookieMonitor实例
        """
        if not self.is_initialized:
            raise Exception("应用管理器未初始化，请先调用initialize()")
        
        print("🔄 正在创建闲鱼Cookie监控器...")
        
        # 使用已初始化的浏览器管理器创建监控器
        monitor = GoofishCookieMonitor(self.browser_manager)
        await monitor.initialize(headless=self.browser_manager.is_browser_active(), auto_start_monitor=auto_start_monitor)
        
        # 记录监控器以便后续管理
        self.monitors.append(monitor)
        
        print("✅ 闲鱼Cookie监控器创建完成")
        return monitor
    
    async def create_stealth_page(self):
        """
        创建带反检测功能的页面
        
        Returns:
            Page对象
        """
        if not self.is_initialized:
            raise Exception("应用管理器未初始化，请先调用initialize()")
        
        return await self.browser_manager.create_stealth_page()
    
    async def get_browser(self):
        """
        获取浏览器实例
        
        Returns:
            Browser对象
        """
        if not self.is_initialized:
            raise Exception("应用管理器未初始化，请先调用initialize()")
        
        return await self.browser_manager.get_browser()
    
    def get_monitors(self) -> List[GoofishCookieMonitor]:
        """
        获取所有监控器
        
        Returns:
            监控器列表
        """
        return self.monitors.copy()
    
    async def close_all_monitors(self):
        """关闭所有监控器"""
        print("🔄 正在关闭所有监控器...")
        
        for monitor in self.monitors:
            try:
                await monitor.close()
            except Exception as e:
                print(f"⚠️  关闭监控器时出错: {str(e)}")
        
        self.monitors.clear()
        print("✅ 所有监控器已关闭")
    
    async def close(self):
        """关闭应用管理器和所有资源"""
        print("🔄 正在关闭应用管理器...")
        
        # 1. 关闭所有监控器
        await self.close_all_monitors()
        
        # 2. 关闭浏览器
        if self.browser_manager:
            await self.browser_manager.close_browser()
            self.browser_manager = None
        
        self.is_initialized = False
        print("✅ 应用管理器已关闭")
    
    def get_status(self) -> dict:
        """
        获取应用管理器状态
        
        Returns:
            状态信息字典
        """
        return {
            'is_initialized': self.is_initialized,
            'browser_active': self.browser_manager.is_browser_active() if self.browser_manager else False,
            'monitors_count': len(self.monitors),
            'monitors_status': [monitor.get_monitor_status() for monitor in self.monitors]
        }


# 全局应用管理器实例
_app_manager = None


async def get_app_manager(headless: bool = False) -> AppManager:
    """
    获取全局应用管理器实例
    
    Args:
        headless: 是否以无头模式运行（仅在首次初始化时有效）
        
    Returns:
        AppManager实例
    """
    global _app_manager
    
    if _app_manager is None:
        _app_manager = AppManager()
        await _app_manager.initialize(headless)
    
    return _app_manager


async def create_goofish_monitor_managed(headless: bool = False, auto_start_monitor: bool = True) -> GoofishCookieMonitor:
    """
    通过应用管理器创建闲鱼监控器的便捷函数
    
    Args:
        headless: 是否以无头模式运行
        auto_start_monitor: 是否自动启动监控
        
    Returns:
        GoofishCookieMonitor实例
    """
    app_manager = await get_app_manager(headless)
    return await app_manager.create_goofish_monitor(auto_start_monitor)


async def create_stealth_page_managed(headless: bool = False):
    """
    通过应用管理器创建反检测页面的便捷函数
    
    Args:
        headless: 是否以无头模式运行
        
    Returns:
        Page对象
    """
    app_manager = await get_app_manager(headless)
    return await app_manager.create_stealth_page()


async def close_app():
    """关闭应用的便捷函数"""
    global _app_manager
    
    if _app_manager:
        await _app_manager.close()
        _app_manager = None


async def get_app_status() -> dict:
    """获取应用状态的便捷函数"""
    global _app_manager
    
    if _app_manager:
        return _app_manager.get_status()
    else:
        return {'is_initialized': False, 'message': '应用管理器未初始化'}


# 使用示例
async def example_usage():
    """使用示例"""
    try:
        # 方法1：直接使用便捷函数（推荐）
        print("🎯 方法1：使用便捷函数")
        monitor = await create_goofish_monitor_managed(headless=False)
        page = await monitor.get_page()
        
        # 使用页面进行操作
        title = await page.title()
        print(f"页面标题: {title}")
        
        # 方法2：使用应用管理器（高级用法）
        print("\n🎯 方法2：使用应用管理器")
        app_manager = await get_app_manager()
        
        # 创建额外的页面
        extra_page = await app_manager.create_stealth_page()
        await extra_page.goto('https://example.com')
        extra_title = await extra_page.title()
        print(f"额外页面标题: {extra_title}")
        
        # 获取状态
        status = await get_app_status()
        print(f"应用状态: {status}")
        
        # 清理资源
        await extra_page.close()
        
    except Exception as e:
        print(f"❌ 示例运行出错: {str(e)}")
    
    finally:
        # 关闭应用
        await close_app()


if __name__ == "__main__":
    asyncio.run(example_usage())
