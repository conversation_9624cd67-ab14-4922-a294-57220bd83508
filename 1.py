import datetime
import os
import platform
import subprocess
import asyncio
import time
from pathlib import Path
from typing import Optional, List, Dict, Any

# 导入pyppeteer相关模块
try:
    from pyppeteer import launch
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False
    print("⚠️  Pyppeteer未安装，请运行: pip install pyppeteer")

# 尝试导入pyppeteer_stealth
try:
    from pyppeteer_stealth import stealth
    STEALTH_AVAILABLE = True
    print("✅ pyppeteer_stealth已加载")
except ImportError:
    STEALTH_AVAILABLE = False
    print("❌ pyppeteer_stealth未安装，请运行: pip install pyppeteer-stealth")


def find_chrome_path() -> Optional[str]:
    """
    跨平台查找Chrome浏览器可执行文件路径

    支持的系统:
    - Windows: 查找Chrome.exe
    - macOS: 查找Google Chrome.app
    - Linux: 查找google-chrome或chromium-browser

    Returns:
        str: Chrome可执行文件的完整路径，如果未找到则返回None
    """
    system = platform.system().lower()

    if system == "windows":
        return _find_chrome_windows()
    elif system == "darwin":  # macOS
        return _find_chrome_macos()
    elif system == "linux":
        return _find_chrome_linux()
    else:
        print(f"不支持的操作系统: {system}")
        return None


def _find_chrome_windows() -> Optional[str]:
    """在Windows系统中查找Chrome路径"""
    # Windows常见的Chrome安装路径
    possible_paths = [
        # 用户级安装
        os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
        # 系统级安装
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        # 便携版可能的位置
        r"C:\Users\<USER>\Desktop\Google Chrome\chrome.exe",
    ]

    # 检查常见路径
    for path in possible_paths:
        if os.path.isfile(path):
            return path

    # 通过注册表查找
    try:
        import winreg
        # 查找默认浏览器注册表项
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                           r"Software\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice") as key:
            prog_id = winreg.QueryValueEx(key, "ProgId")[0]

        if "chrome" in prog_id.lower():
            with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT,
                               f"{prog_id}\\shell\\open\\command") as key:
                command = winreg.QueryValueEx(key, "")[0]
                # 提取可执行文件路径
                chrome_path = command.split('"')[1] if '"' in command else command.split()[0]
                if os.path.isfile(chrome_path):
                    return chrome_path
    except (ImportError, OSError, IndexError):
        pass

    # 通过where命令查找
    try:
        result = subprocess.run(['where', 'chrome'],
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            chrome_path = result.stdout.strip().split('\n')[0]
            if os.path.isfile(chrome_path):
                return chrome_path
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    return None


def _find_chrome_macos() -> Optional[str]:
    """在macOS系统中查找Chrome路径"""
    # macOS常见的Chrome安装路径
    possible_paths = [
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
        "/Applications/Chromium.app/Contents/MacOS/Chromium",
        # 用户Applications目录
        os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
    ]

    # 检查常见路径
    for path in possible_paths:
        if os.path.isfile(path):
            return path

    # 使用mdfind命令查找
    try:
        result = subprocess.run(['mdfind', 'kMDItemCFBundleIdentifier == "com.google.Chrome"'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            app_paths = result.stdout.strip().split('\n')
            for app_path in app_paths:
                if app_path and os.path.isdir(app_path):
                    chrome_executable = os.path.join(app_path, "Contents/MacOS/Google Chrome")
                    if os.path.isfile(chrome_executable):
                        return chrome_executable
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    # 使用which命令查找
    try:
        result = subprocess.run(['which', 'google-chrome'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            chrome_path = result.stdout.strip()
            if os.path.isfile(chrome_path):
                return chrome_path
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    return None


def _find_chrome_linux() -> Optional[str]:
    """在Linux系统中查找Chrome路径"""
    # Linux常见的Chrome安装路径
    possible_paths = [
        "/usr/bin/google-chrome",
        "/usr/bin/google-chrome-stable",
        "/usr/bin/google-chrome-beta",
        "/usr/bin/google-chrome-unstable",
        "/usr/bin/chromium-browser",
        "/usr/bin/chromium",
        "/snap/bin/chromium",
        "/opt/google/chrome/chrome",
        "/opt/google/chrome/google-chrome",
    ]

    # 检查常见路径
    for path in possible_paths:
        if os.path.isfile(path):
            return path

    # 使用which命令查找
    chrome_commands = [
        "google-chrome",
        "google-chrome-stable",
        "google-chrome-beta",
        "google-chrome-unstable",
        "chromium-browser",
        "chromium"
    ]

    for cmd in chrome_commands:
        try:
            result = subprocess.run(['which', cmd],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                chrome_path = result.stdout.strip()
                if os.path.isfile(chrome_path):
                    return chrome_path
        except (subprocess.SubprocessError, FileNotFoundError):
            continue

    # 检查flatpak安装的Chrome
    try:
        result = subprocess.run(['flatpak', 'list', '--app'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            if 'com.google.Chrome' in result.stdout:
                return 'flatpak run com.google.Chrome'
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    return None



async def launch_pyppeteer_with_chrome(chrome_path: str, headless: bool = True) -> Optional[object]:
    """
    使用指定的Chrome路径启动Pyppeteer浏览器

    Args:
        chrome_path: Chrome可执行文件路径
        headless: 是否以无头模式运行

    Returns:
        Browser对象，如果启动失败则返回None
    """
    if not PYPPETEER_AVAILABLE:
        print("❌ Pyppeteer未安装，无法启动浏览器")
        return None

    if not STEALTH_AVAILABLE:
        print("❌ pyppeteer-stealth未安装，无法启动反检测浏览器")
        return None

    if not chrome_path or not os.path.exists(chrome_path):
        print("❌ Chrome路径无效")
        return None

    try:
        print(f"🚀 正在使用Chrome启动Pyppeteer浏览器...")
        print(f"   Chrome路径: {chrome_path}")
        print(f"   无头模式: {'是' if headless else '否'}")
        print(f"   反检测模式: 是 (pyppeteer-stealth)")

        # 反检测启动参数
        args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--disable-blink-features=AutomationControlled',  # 关键反检测参数
            '--disable-infobars',                             # 禁用信息栏
            '--disable-extensions',                           # 禁用扩展
            '--no-default-browser-check',                     # 不检查默认浏览器
            '--disable-default-apps',                         # 禁用默认应用
        ]

        # 启动浏览器（忽略默认的自动化参数）
        browser = await launch({
            'executablePath': chrome_path,
            'headless': headless,
            'args': args,
            'ignoreHTTPSErrors': True,
            'autoClose': False,
            'ignoreDefaultArgs': [
                '--enable-automation',      # 关键：忽略自动化标识参数
                '--enable-blink-features=AutomationControlled'  # 忽略自动化控制特性
            ]
        })

        print("✅ Pyppeteer浏览器启动成功！")
        return browser

    except Exception as e:
        print(f"❌ 启动Pyppeteer浏览器失败: {str(e)}")
        return None

# def log_request(request):
#     target_url  = "https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0"
#     if target_url  in request.url:  # 过滤特定请求
#         print(f"[Request] {request.method} {request.url}")
#         # 查看请求头
#         print(f"Headers: {request.headers}")
#
#
# def log_response(response):
#     if response.status == 200 and 'json' in response.headers.get('content-type', ''):
#         print(f"[Response] {response.status} {response.url}")



async def demo_pyppeteer_usage(browser):
    """
    演示Pyppeteer的基本使用（使用pyppeteer-stealth）

    Args:
        browser: Pyppeteer浏览器对象
    """
    try:
        print("\n🌐 开始演示Pyppeteer功能...")

        # 创建新页面
        page = await browser.newPage()
        print("✅ 创建新页面成功")

        # 应用pyppeteer-stealth反检测
        await stealth(page)
        print("✅ pyppeteer-stealth反检测已应用")

        # 设置视口大小
        await page.setViewport({'width': 1280, 'height': 720})


        try:
            await page.goto('https://www.goofish.com/', {'waitUntil': 'networkidle0', 'timeout': 30000})
            print("✅ Bot检测网站加载完成")

            # 等待页面完全加载和检测完成
            await asyncio.sleep(3)

        except Exception as e:
            print(f"❌ 演示过程中出错: {str(e)}")
            exit()

        # 获取指定URL的Cookie
        cookies = await page.cookies('https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0')

        # 打印带详细信息的Cookie
        print("Cookie详情:")
        print("=" * 80)
        for cookie in cookies:
            # 格式化到期时间
            if 'expires' in cookie and cookie['expires'] > 0:
                # 转换为人类可读的时间格式
                expires_dt = datetime.datetime.fromtimestamp(cookie['expires'])
                expires_str = expires_dt.strftime('%Y-%m-%d %H:%M:%S')
                remaining = (expires_dt - datetime.datetime.now()).days
                expires_info = f"{expires_str} (剩余{remaining}天)"
            else:
                expires_info = "会话结束即过期"

            # 格式化其他属性
            same_site = cookie.get('sameSite', '未指定')
            http_only = '是' if cookie.get('httpOnly', False) else '否'
            secure = '是' if cookie.get('secure', False) else '否'

            # 打印详细信息
            print(f"🍪 名称: {cookie['name']}")
            print(f"  值: {cookie['value']}")
            print(f"  作用域: {cookie['domain']}{cookie['path']}")
            print(f"  有效期: {expires_info}")
            print(f"  同站策略: {same_site}")
            print(f"  HTTP Only: {http_only}")
            print(f"  安全传输: {secure}")
            print(f"  大小: {cookie.get('size', '未知')} bytes")
            print("-" * 60)
        # 获取页面标题
        title = await page.title()
        print(f"📄 页面标题: {title}")

        # 检查_m_h5_tk_enc Cookie有效期
        await ensure_valid_m_h5_tk_enc(page)

        # 演示定期检查Cookie的功能
        print("\n🔄 开始定期检查Cookie有效期（每30秒检查一次）...")
        for i in range(3):  # 检查3次作为演示
            print(f"\n--- 第{i+1}次检查 ---")
            is_valid = await ensure_valid_m_h5_tk_enc(page)

            if not is_valid:
                print("❌ Cookie无效，建议停止API请求")
                break

            if i < 2:  # 不是最后一次检查
                print("⏳ 等待30秒后进行下次检查...")
                await asyncio.sleep(30)


    except Exception as e:
        print(f"❌ 演示过程中出错: {str(e)}")


async def check_m_h5_tk_enc_validity(page) -> bool:
    """
    检查_m_h5_tk_enc Cookie的有效期

    Args:
        page: Pyppeteer页面对象

    Returns:
        bool: True表示Cookie有效，False表示已过期或不存在
    """
    try:
        # 获取闲鱼域名的所有Cookie
        cookies = await page.cookies('https://www.goofish.com')

        # 查找_m_h5_tk_enc Cookie
        m_h5_tk_enc_cookie = None
        for cookie in cookies:
            if cookie['name'] == '_m_h5_tk_enc':
                m_h5_tk_enc_cookie = cookie
                break

        if not m_h5_tk_enc_cookie:
            print("⚠️  未找到_m_h5_tk_enc Cookie")
            return False

        # 获取Cookie信息
        cookie_value = m_h5_tk_enc_cookie['value']
        expires = m_h5_tk_enc_cookie.get('expires', None)

        print(f"🍪 _m_h5_tk_enc Cookie信息:")
        print(f"   值: {cookie_value}")

        if expires is None:
            print("   有效期: 会话Cookie（浏览器关闭时过期）")
            return True

        # 将expires时间戳转换为datetime
        if isinstance(expires, (int, float)):
            expire_time = datetime.datetime.fromtimestamp(expires)
        else:
            # 如果expires是字符串，尝试解析
            try:
                expire_time = datetime.datetime.fromisoformat(str(expires).replace('Z', '+00:00'))
            except:
                print(f"   有效期: 无法解析时间格式 ({expires})")
                return True

        current_time = datetime.datetime.now()

        # 计算剩余时间
        time_remaining = expire_time - current_time

        print(f"   有效期: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")

        if time_remaining.total_seconds() > 0:
            days_remaining = time_remaining.days
            hours_remaining = time_remaining.seconds // 3600
            minutes_remaining = (time_remaining.seconds % 3600) // 60

            print(f"   剩余时间: {days_remaining}天 {hours_remaining}小时 {minutes_remaining}分钟")

            # 如果剩余时间少于30分钟，认为即将过期
            if time_remaining.total_seconds() < 1800:  # 30分钟 = 1800秒
                print("⚠️  Cookie即将过期（少于30分钟）")
                return False
            else:
                print("✅ Cookie有效")
                return True
        else:
            print("❌ Cookie已过期")
            return False

    except Exception as e:
        print(f"❌ 检查Cookie有效期时出错: {str(e)}")
        return False


async def refresh_goofish_page(page):
    """
    刷新闲鱼页面以更新Cookie

    Args:
        page: Pyppeteer页面对象
    """
    try:
        print("🔄 正在刷新闲鱼页面以更新Cookie...")
        await page.goto('https://www.goofish.com/', {'waitUntil': 'networkidle2', 'timeout': 30000})

        # 等待页面加载完成
        await asyncio.sleep(3)

        # 可以在这里添加一些交互操作来确保Cookie更新
        # 比如滚动页面、点击某些元素等
        await page.evaluate('window.scrollTo(0, 500)')
        await asyncio.sleep(1)
        await page.evaluate('window.scrollTo(0, 0)')

        print("✅ 页面刷新完成")

    except Exception as e:
        print(f"❌ 刷新页面时出错: {str(e)}")


async def ensure_valid_m_h5_tk_enc(page):
    """
    确保_m_h5_tk_enc Cookie有效，如果无效则刷新页面

    Args:
        page: Pyppeteer页面对象

    Returns:
        bool: True表示Cookie有效或已成功刷新，False表示刷新失败
    """
    print("\n🔍 检查_m_h5_tk_enc Cookie有效期...")

    # 检查Cookie有效期
    is_valid = await check_m_h5_tk_enc_validity(page)

    if not is_valid:
        print("🔄 Cookie无效或即将过期，正在刷新页面...")
        await refresh_goofish_page(page)

        # 刷新后再次检查
        print("\n🔍 刷新后重新检查Cookie...")
        is_valid = await check_m_h5_tk_enc_validity(page)

        if is_valid:
            print("✅ Cookie刷新成功")
        else:
            print("❌ Cookie刷新失败，可能需要重新登录")

    return is_valid


async def start_cookie_monitor(page, check_interval=300):
    """
    启动Cookie监控器，定期检查_m_h5_tk_enc有效期

    Args:
        page: Pyppeteer页面对象
        check_interval: 检查间隔（秒），默认5分钟
    """
    print(f"🔄 启动Cookie监控器，每{check_interval//60}分钟检查一次...")

    while True:
        try:
            await asyncio.sleep(check_interval)

            print(f"\n⏰ [{datetime.datetime.now().strftime('%H:%M:%S')}] 定期检查Cookie有效期...")
            is_valid = await ensure_valid_m_h5_tk_enc(page)

            if not is_valid:
                print("❌ Cookie监控检测到无效Cookie，请注意！")
            else:
                print("✅ Cookie监控：状态正常")

        except Exception as e:
            print(f"❌ Cookie监控器出错: {str(e)}")
            await asyncio.sleep(60)  # 出错后等待1分钟再继续


async def demo_with_cookie_monitoring(browser):
    """
    演示带Cookie监控的功能

    Args:
        browser: Pyppeteer浏览器对象
    """
    try:
        print("\n🌐 开始演示带Cookie监控的Pyppeteer功能...")

        # 创建新页面
        page = await browser.newPage()
        print("✅ 创建新页面成功")

        # 应用pyppeteer-stealth反检测
        await stealth(page)
        print("✅ pyppeteer-stealth反检测已应用")

        # 设置视口大小
        await page.setViewport({'width': 1280, 'height': 720})

        # 访问闲鱼网站
        print("🔗 正在访问闲鱼网站...")
        await page.goto('https://www.goofish.com/', {'waitUntil': 'networkidle2', 'timeout': 30000})
        print("✅ 闲鱼网站加载完成")

        # 初始Cookie检查
        await ensure_valid_m_h5_tk_enc(page)

        # 启动后台Cookie监控（每2分钟检查一次，用于演示）
        monitor_task = asyncio.create_task(start_cookie_monitor(page, check_interval=120))

        # 模拟长时间运行的任务
        print("\n🚀 开始模拟长时间运行的任务...")
        print("💡 Cookie监控器将在后台运行，每2分钟检查一次")

        for i in range(5):  # 模拟运行5次任务
            print(f"\n--- 执行任务 {i+1}/5 ---")

            # 模拟一些API请求或其他操作
            await asyncio.sleep(30)  # 模拟30秒的工作

            # 手动检查一次Cookie（可选）
            if i == 2:  # 在第3次任务时手动检查
                print("🔍 手动检查Cookie状态...")
                await ensure_valid_m_h5_tk_enc(page)

        print("\n✅ 所有任务完成")

        # 取消监控任务
        monitor_task.cancel()
        print("🛑 Cookie监控器已停止")

        # 关闭页面
        await page.close()
        print("✅ 页面已关闭")

    except Exception as e:
        print(f"❌ 演示过程中出错: {str(e)}")


async def main_async():
    """异步主函数 - 查找Chrome并启动Pyppeteer"""
    print("=== Chrome路径查找与Pyppeteer启动 ===\n")
    print(f"当前操作系统: {platform.system()}")

    # 查找Chrome路径
    print("\n🔍 正在查找Chrome浏览器...")
    chrome_path = find_chrome_path()

    if not chrome_path:
        print("❌ 未找到Chrome浏览器")
        print("请确保已安装Google Chrome或Chromium浏览器")
        return

    print(f"✅ 找到Chrome路径: {chrome_path}")

    # 验证Chrome路径
    if chrome_path.startswith('flatpak'):
        print("📦 检测到Flatpak安装的Chrome")
    elif os.path.isfile(chrome_path) and os.access(chrome_path, os.X_OK):
        print("✅ Chrome可执行文件验证通过")
    elif os.path.isfile(chrome_path):
        print("⚠️  Chrome文件存在但可能无执行权限")
    else:
        print("⚠️  Chrome路径可能不是文件")

    # 启动Pyppeteer（使用pyppeteer-stealth）
    browser = await launch_pyppeteer_with_chrome(chrome_path, headless=False)

    if browser:
        try:
            # 选择演示模式
            print("\n🎯 选择演示模式:")
            print("1. 基础演示（包含Cookie检查）")
            print("2. Cookie监控演示（后台持续监控）")

            # 这里可以添加用户输入，现在默认使用Cookie监控演示
            demo_mode = 2

            if demo_mode == 1:
                # 基础演示功能
                await demo_pyppeteer_usage(browser)
            else:
                # Cookie监控演示
                await demo_with_cookie_monitoring(browser)

            print("\n⏳ 浏览器将在5秒后关闭...")
            await asyncio.sleep(5)

        finally:
            # 确保浏览器被关闭
            # await browser.close()
            print("🔒 浏览器已关闭")

    print("\n✅ 程序执行完成")


def main():
    """同步主函数 - 运行异步任务"""
    if not PYPPETEER_AVAILABLE:
        print("❌ 请先安装Pyppeteer: pip install pyppeteer")
        return

    # 运行异步主函数
    asyncio.run(main_async())



if __name__ == "__main__":
    # 运行主程序 - 查找Chrome并启动Pyppeteer
    main()
