"""
使用示例：单例浏览器管理器 + 闲鱼Cookie监控器

演示如何使用封装好的模块进行开发

Author: AI Assistant
Date: 2025-07-29
"""

import asyncio
from browser_manager import get_singleton_browser, create_stealth_page, close_singleton_browser, SingletonBrowserManager
from goofish_monitor import create_goofish_monitor, get_goofish_cookies
from app_manager import get_app_manager, create_goofish_monitor_managed, create_stealth_page_managed, close_app


async def example_basic_usage():
    """基础使用示例"""
    print("🎯 基础使用示例")
    print("=" * 50)
    
    try:
        # 1. 创建闲鱼监控器（自动使用单例浏览器）
        print("\n📋 步骤1: 创建闲鱼Cookie监控器")
        monitor = await create_goofish_monitor(headless=False, auto_start_monitor=True)
        
        # 2. 获取页面对象供后续使用
        print("\n📋 步骤2: 获取页面对象")
        page = await monitor.get_page()
        print(f"✅ 页面对象类型: {type(page)}")
        
        # 3. 使用页面进行操作
        print("\n📋 步骤3: 使用页面进行操作")
        title = await page.title()
        print(f"📄 页面标题: {title}")
        
        # 4. 获取Cookie信息
        print("\n📋 步骤4: 获取Cookie信息")
        cookies = await monitor.get_cookies()
        print(f"🍪 Cookie数量: {len(cookies)}")
        
        # 获取目标Cookie详细信息
        target_cookies = await monitor.get_target_cookies()
        for name, info in target_cookies.items():
            print(f"🎯 {name}: {info['value'][:20]}...")
        
        # 5. 模拟运行一段时间（监控器在后台工作）
        print("\n📋 步骤5: 模拟长时间运行（监控器后台工作）")
        for i in range(3):
            print(f"   执行任务 {i+1}/3...")
            await asyncio.sleep(5)  # 模拟5秒工作
        
        # 6. 清理资源
        print("\n📋 步骤6: 清理资源")
        await monitor.close()
        
    except Exception as e:
        print(f"❌ 基础使用示例出错: {str(e)}")


async def example_advanced_usage():
    """高级使用示例"""
    print("\n🚀 高级使用示例")
    print("=" * 50)
    
    try:
        # 1. 获取单例浏览器（可以创建多个页面）
        print("\n📋 步骤1: 获取单例浏览器")
        browser = await get_singleton_browser(headless=False)
        
        # 2. 创建多个页面（共享同一浏览器实例）
        print("\n📋 步骤2: 创建多个页面")
        page1 = await create_stealth_page()
        page2 = await create_stealth_page()
        
        # 3. 创建闲鱼监控器
        print("\n📋 步骤3: 创建闲鱼监控器")
        monitor = await create_goofish_monitor(headless=False, auto_start_monitor=False)
        monitor_page = await monitor.get_page()
        
        # 4. 所有页面都使用同一个浏览器实例
        print("\n📋 步骤4: 验证页面共享浏览器")
        print(f"✅ 页面1浏览器: {id(page1.browser)}")
        print(f"✅ 页面2浏览器: {id(page2.browser)}")
        print(f"✅ 监控页面浏览器: {id(monitor_page.browser)}")
        print("🎯 所有页面共享同一个浏览器实例！")
        
        # 5. 分别使用不同页面
        print("\n📋 步骤5: 分别使用不同页面")
        
        # 页面1访问百度
        await page1.goto('https://www.baidu.com')
        title1 = await page1.title()
        print(f"📄 页面1标题: {title1}")
        
        # 页面2访问谷歌
        await page2.goto('https://www.google.com')
        title2 = await page2.title()
        print(f"📄 页面2标题: {title2}")
        
        # 监控页面已经在闲鱼
        title3 = await monitor_page.title()
        print(f"📄 监控页面标题: {title3}")
        
        # 6. 手动启动监控
        print("\n📋 步骤6: 手动启动Cookie监控")
        await monitor.start_monitoring(check_interval=60)  # 1分钟检查一次
        
        # 7. 模拟工作
        print("\n📋 步骤7: 模拟并发工作")
        await asyncio.sleep(10)
        
        # 8. 清理资源
        print("\n📋 步骤8: 清理资源")
        await page1.close()
        await page2.close()
        await monitor.close()
        
    except Exception as e:
        print(f"❌ 高级使用示例出错: {str(e)}")


async def example_cookie_operations():
    """Cookie操作示例"""
    print("\n🍪 Cookie操作示例")
    print("=" * 50)
    
    try:
        # 1. 快速获取Cookie（便捷函数）
        print("\n📋 步骤1: 快速获取Cookie")
        cookies = await get_goofish_cookies()
        print(f"🍪 快速获取到 {len(cookies)} 个Cookie")
        
        # 2. 创建监控器进行详细操作
        print("\n📋 步骤2: 创建监控器进行详细操作")
        monitor = await create_goofish_monitor(headless=True, auto_start_monitor=False)
        
        # 3. 检查特定Cookie有效期
        print("\n📋 步骤3: 检查Cookie有效期")
        is_valid = await monitor.check_cookie_validity('_m_h5_tk_enc')
        print(f"🔍 _m_h5_tk_enc 有效性: {'有效' if is_valid else '无效'}")
        
        # 4. 获取目标Cookie详细信息
        print("\n📋 步骤4: 获取目标Cookie详细信息")
        target_cookies = await monitor.get_target_cookies()
        for name, info in target_cookies.items():
            print(f"🎯 {name}:")
            print(f"   值: {info['value'][:30]}...")
            print(f"   域名: {info.get('domain', 'N/A')}")
            print(f"   路径: {info.get('path', 'N/A')}")
            if info.get('expires'):
                import datetime
                expire_time = datetime.datetime.fromtimestamp(info['expires'])
                print(f"   过期时间: {expire_time}")
        
        # 5. 测试页面刷新功能
        print("\n📋 步骤5: 测试页面刷新功能")
        await monitor.refresh_page()
        
        # 6. 获取监控器状态
        print("\n📋 步骤6: 获取监控器状态")
        status = monitor.get_monitor_status()
        print(f"📊 监控器状态: {status}")
        
        # 7. 清理
        await monitor.close()
        
    except Exception as e:
        print(f"❌ Cookie操作示例出错: {str(e)}")


async def example_real_world_scenario():
    """真实世界使用场景示例"""
    print("\n🌍 真实世界使用场景示例")
    print("=" * 50)
    
    try:
        # 场景：需要长时间运行的爬虫任务，需要保持Cookie有效
        print("📋 场景：长时间运行的爬虫任务")
        
        # 1. 初始化监控器
        monitor = await create_goofish_monitor(headless=True, auto_start_monitor=True)
        page = await monitor.get_page()
        
        # 2. 模拟爬虫任务
        print("\n🕷️  开始爬虫任务...")
        for i in range(10):  # 模拟10轮爬取
            print(f"   第{i+1}轮爬取...")
            
            # 在每轮爬取前检查Cookie
            is_valid = await monitor.check_cookie_validity('_m_h5_tk_enc')
            if not is_valid:
                print("⚠️  Cookie无效，停止爬取")
                break
            
            # 模拟API请求或页面操作
            await asyncio.sleep(2)  # 模拟2秒的爬取工作
            
            # 获取一些数据（示例）
            title = await page.title()
            cookies = await monitor.get_cookies()
            print(f"   ✅ 第{i+1}轮完成，页面: {title}, Cookie数: {len(cookies)}")
        
        print("✅ 爬虫任务完成")
        
        # 3. 清理
        await monitor.close()
        
    except Exception as e:
        print(f"❌ 真实场景示例出错: {str(e)}")


async def main():
    """主函数"""
    print("🎯 单例浏览器管理器 + 闲鱼Cookie监控器 使用示例")
    print("=" * 80)
    
    try:
        # 运行各种示例
        await example_basic_usage()
        await example_advanced_usage()
        await example_cookie_operations()
        await example_real_world_scenario()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 主函数出错: {str(e)}")
    
    finally:
        # 确保浏览器被关闭
        print("\n🔒 确保浏览器资源被释放...")
        await close_singleton_browser()
        print("✅ 程序结束")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
